# Quality Upgrade and Real Debrid Cleanup Implementation

## Overview

This implementation adds functionality to automatically remove old downloads from Real Debrid when a better quality version becomes available. This ensures that only one episode remains in Real Debrid, preventing storage waste and confusion.

## Key Features

### 1. Quality Comparison System

Added comprehensive quality comparison methods to the `MediaQuality` model:

- **Resolution Priority**: 4K (4000) > 1080p (1080) > 720p (720) > 480p (480) > SD (240)
- **Source Priority**: REMUX (1000) > BluRay (900) > WEB-DL (800) > WEBRip (700) > WEB (600) > HDTV (500) > DVD (400)
- **Codec Priority**: AV1 (300) > h265/HEVC (200) > h264/AVC (100) > MPEG2 (50)

Methods added:
- `get_total_score()`: Calculates combined quality score
- `is_better_than(other_quality)`: Simple quality comparison
- `is_significantly_better_than(other_quality, threshold=100)`: Prevents minor upgrades

### 2. Download Status Cleanup

Enhanced `DownloadStatus` model with:

- `get_quality_object()`: Extracts quality info from torrent title
- `cleanup_real_debrid()`: Removes torrent from Real Debrid and clears IDs

### 3. Monitoring Command Enhancements

Modified `monitor_downloads.py` to:

- Detect quality upgrades when episodes become available in Plex
- Automatically clean up old Real Debrid downloads when better quality is detected
- Use significant quality difference threshold to avoid unnecessary cleanups

### 4. Download Initiation Logic

Updated both manual and auto-download flows in `views.py`:

#### Manual Downloads (`jackett_download`)
- Check for existing active downloads
- Allow quality upgrades but prevent duplicate active downloads
- Clean up old Real Debrid downloads when starting a better quality download

#### Auto Downloads (`auto_download_episode`)
- Skip episodes with active downloads
- Only proceed with quality upgrades for completed downloads
- Clean up old downloads before starting new ones
- Skip downloads that aren't quality improvements

## Workflow Examples

### Scenario 1: Manual Quality Upgrade
1. User has 720p WEB-DL episode downloaded and completed
2. User manually selects 1080p BluRay torrent
3. System detects this is a quality upgrade (score: 2080 vs 1620)
4. Old 720p download is removed from Real Debrid
5. New 1080p download starts

### Scenario 2: Auto-Download Quality Upgrade
1. Episode has completed 1080p WEB-DL download (score: 1980)
2. Auto-download finds 4K WEB-DL h265 torrent (score: 5000)
3. System detects significant quality improvement
4. Old 1080p download is cleaned up from Real Debrid
5. New 4K download starts

### Scenario 3: Monitoring Detection
1. Episode download completes and appears in Plex with better quality than expected
2. Monitor detects quality upgrade during availability check
3. System looks for other completed downloads for same episode
4. Removes inferior quality downloads from Real Debrid

## Quality Score Examples

From testing:
- 720p WEB-DL h264: 1620 points
- 1080p WEB-DL h264: 1980 points  
- 1080p BluRay h264: 2080 points
- 4K WEB-DL h265: 5000 points

## Configuration

The system uses a default threshold of 100 points for "significant" upgrades. This means:
- 1080p WEB-DL → 720p WEB-DL: Significant upgrade (360 point difference)
- 1080p BluRay → 1080p WEB-DL: Not significant (100 point difference)
- 4K WEB-DL → 1080p BluRay: Significant upgrade (2920 point difference)

## Error Handling

- Failed Real Debrid cleanup is logged but doesn't prevent new downloads
- Quality comparison failures default to allowing the download
- Multiple active downloads are handled gracefully
- Dry-run mode supported in monitoring command

## Benefits

1. **Storage Efficiency**: Only one version per episode in Real Debrid
2. **Quality Optimization**: Automatically upgrades to better versions
3. **User Control**: Manual downloads can override auto-downloads
4. **Safe Operation**: Significant upgrade threshold prevents unnecessary churn
5. **Monitoring Integration**: Works with existing Plex/Overseerr monitoring

## Files Modified

- `media_requests/models.py`: Quality comparison and cleanup methods
- `media_requests/management/commands/monitor_downloads.py`: Quality upgrade detection
- `media_requests/views.py`: Download initiation logic with cleanup
- `media_requests/real_debrid_service.py`: Cleanup functionality (already existed)
